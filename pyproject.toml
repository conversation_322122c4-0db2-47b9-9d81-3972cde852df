[project]
name = "googleadk"
version = "0.1.0"
description = "Dependencies for Oracle"
authors = [
    {name = "alshen12",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.13,<4"
dependencies = [
    "google-adk (>=1.1.1,<2.0.0)",
    "py-clob-client (>=0.23.0,<0.24.0)",
    "inquirer (>=3.4.0,<4.0.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
