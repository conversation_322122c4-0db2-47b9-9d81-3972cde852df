import httpx
import json

# The endpoint for fetching events from the Gamma API
gamma_events_endpoint = "https://gamma-api.polymarket.com/events"

# --- Define Your Filters and Sorting Options ---

# 1. Specify the category you want to search for.
#    Options include: "Politics", "Sports", "Crypto", "Business", etc.
desired_category = "Politics"

# 2. Set sorting parameters for the fastest resolving events.
#    - order='endDate': The field we want to sort by.
#    - ascending='true': Sorts from the nearest date to the furthest.
sort_by_field = "endDate"
sort_order = "true"

print(f"Fetching the 10 fastest-resolving open events in the '{desired_category}' category...")

try:
    # Combine all parameters for the API request
    params = {
        "category": desired_category,
        "order": sort_by_field,
        "ascending": sort_order,
        "archived": "false",    # Ensure the event is not archived
        "active": "true",       # Ensure the event is active
        "closed": "false",      # Ensure the event is not closed
        "limit": 10             # Get the top 10 results
    }

    # Make the single GET request to the API
    response = httpx.get(gamma_events_endpoint, params=params, timeout=30.0)
    response.raise_for_status()
    
    # Convert the JSON response into a Python list
    fastest_events = response.json()

    print(f"\nSuccessfully fetched {len(fastest_events)} events.")

    # --- Output to JSON ---

    # Convert the list of events into a nicely formatted JSON string
    final_json_output = json.dumps(fastest_events, indent=2)

    # Define the output filename
    output_filename = "fastest_resolving_politics_events.json"

    # Write the JSON string to a file
    with open(output_filename, 'w', encoding='utf-8') as f:
        f.write(final_json_output)

    print(f"The filtered events have been saved to '{output_filename}'")
    
    # Display the titles and end dates to verify the sorting
    print("\n--- Fastest Resolving Events (Sorted by End Date) ---")
    for event in fastest_events:
        end_date = event.get('endDate', 'N/A')
        title = event.get('title', 'N/A')
        print(f"- Ends: {end_date} | Title: {title}")

except httpx.RequestError as e:
    print(f"\nAn error occurred while communicating with the API: {e}")
except json.JSONDecodeError:
    print("\nError: Failed to decode JSON from the API response.")
except Exception as e:
    print(f"\nAn unexpected error occurred: {e}")